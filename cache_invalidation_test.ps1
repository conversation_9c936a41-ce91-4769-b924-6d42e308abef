Write-Host "=== CACHE INVALIDATION TEST ===" -ForegroundColor Yellow

$baseUrl = "http://localhost:5165"

# STEP 1: Cache'i doldur
Write-Host "STEP 1: Filling cache..." -ForegroundColor Cyan
$response1 = Invoke-RestMethod -Uri "$baseUrl/api/member/getallpaginated?pageNumber=1&pageSize=5" -Method GET
Write-Host "  Cache filled. Data count: $($response1.data.items.Count)" -ForegroundColor Green

# STEP 2: Cache HIT'i dogrula
Write-Host "STEP 2: Verifying cache HIT..." -ForegroundColor Cyan
$start = Get-Date
$response2 = Invoke-RestMethod -Uri "$baseUrl/api/member/getallpaginated?pageNumber=1&pageSize=5" -Method GET
$end = Get-Date
$hitTime = ($end - $start).TotalMilliseconds
Write-Host "  Cache HIT confirmed: $([math]::Round($hitTime, 2))ms" -ForegroundColor Green

# STEP 3: Veri guncelle (Cache invalidation tetikle)
Write-Host "STEP 3: Updating data to trigger cache invalidation..." -ForegroundColor Cyan
try {
    # Ilk uyeyi al
    $firstMember = $response1.data.items[0]
    Write-Host "  Updating member: $($firstMember.name) (ID: $($firstMember.memberID))" -ForegroundColor White
    
    # Uyeyi guncelle (name'e timestamp ekle)
    $timestamp = Get-Date -Format "HHmmss"
    $updatedMember = @{
        memberID = $firstMember.memberID
        name = "$($firstMember.name.Split(' ')[0]) UPDATED$timestamp"
        phoneNumber = $firstMember.phoneNumber
        gender = $firstMember.gender
        companyID = $firstMember.companyID
        userID = $firstMember.userID
        isActive = $firstMember.isActive
        scanNumber = $firstMember.scanNumber
        balance = $firstMember.balance
    }
    
    $updateResponse = Invoke-RestMethod -Uri "$baseUrl/api/member/update" -Method POST -Body ($updatedMember | ConvertTo-Json) -ContentType "application/json"
    
    if ($updateResponse.success) {
        Write-Host "  Member updated successfully!" -ForegroundColor Green
    } else {
        Write-Host "  Update failed: $($updateResponse.message)" -ForegroundColor Red
        return
    }
} catch {
    Write-Host "  Update ERROR: $($_.Exception.Message)" -ForegroundColor Red
    return
}

# STEP 4: Cache MISS'i dogrula (invalidation calistigindan emin ol)
Write-Host "STEP 4: Verifying cache MISS after invalidation..." -ForegroundColor Cyan
$start = Get-Date
$response3 = Invoke-RestMethod -Uri "$baseUrl/api/member/getallpaginated?pageNumber=1&pageSize=5" -Method GET
$end = Get-Date
$missTime = ($end - $start).TotalMilliseconds
Write-Host "  Cache MISS confirmed: $([math]::Round($missTime, 2))ms" -ForegroundColor Green

# STEP 5: Guncel veriyi dogrula
Write-Host "STEP 5: Verifying updated data..." -ForegroundColor Cyan
$updatedMemberInList = $response3.data.items | Where-Object { $_.memberID -eq $firstMember.memberID }
if ($updatedMemberInList -and $updatedMemberInList.name -like "*UPDATED*") {
    Write-Host "  SUCCESS: Updated data found in response!" -ForegroundColor Green
    Write-Host "  New name: $($updatedMemberInList.name)" -ForegroundColor Green
} else {
    Write-Host "  ERROR: Updated data not found!" -ForegroundColor Red
}

# STEP 6: Cache yeniden doldur
Write-Host "STEP 6: Refilling cache..." -ForegroundColor Cyan
$start = Get-Date
$response4 = Invoke-RestMethod -Uri "$baseUrl/api/member/getallpaginated?pageNumber=1&pageSize=5" -Method GET
$end = Get-Date
$refillTime = ($end - $start).TotalMilliseconds
Write-Host "  Cache refilled: $([math]::Round($refillTime, 2))ms" -ForegroundColor Green

Write-Host ""
Write-Host "=== CACHE INVALIDATION TEST SUMMARY ===" -ForegroundColor Yellow
Write-Host "  Initial Cache HIT: $([math]::Round($hitTime, 2))ms" -ForegroundColor Green
Write-Host "  After Update MISS: $([math]::Round($missTime, 2))ms" -ForegroundColor Green
Write-Host "  Cache Refill: $([math]::Round($refillTime, 2))ms" -ForegroundColor Green

if ($missTime -gt ($hitTime * 2)) {
    Write-Host "  RESULT: Cache invalidation WORKING! ✅" -ForegroundColor Green
} else {
    Write-Host "  RESULT: Cache invalidation might NOT be working! ❌" -ForegroundColor Red
}
