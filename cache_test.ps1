# GYM PROJECT CACHE TEST SCRIPT
Write-Host "=== GYM PROJECT CACHE ALTYAPISI TEST ===" -ForegroundColor Yellow
Write-Host "Test Tarihi: $(Get-Date)" -ForegroundColor Gray
Write-Host ""

$baseUrl = "http://localhost:5165"
$testResults = @()

# TEST 1: Member GetAllPaginated (Cache: 600s)
Write-Host "TEST 1: api/member/getallpaginated" -ForegroundColor Cyan
Write-Host "Cache Duration: 600 saniye (10 dakika)" -ForegroundColor Gray

try {
    # İlk istek (Cache MISS)
    Write-Host "  1. İlk istek (Cache MISS bekleniyor)..." -ForegroundColor White
    $start1 = Get-Date
    $response1 = Invoke-RestMethod -Uri "$baseUrl/api/member/getallpaginated?pageNumber=1&pageSize=5" -Method GET
    $end1 = Get-Date
    $duration1 = ($end1 - $start1).TotalMilliseconds
    Write-Host "     ✓ Tamamlandı. Süre: $([math]::Round($duration1, 2))ms" -ForegroundColor Green
    Write-Host "     ✓ Veri sayısı: $($response1.data.items.Count)" -ForegroundColor Green

    # 2 saniye bekle
    Start-Sleep -Seconds 2

    # İkinci istek (Cache HIT)
    Write-Host "  2. İkinci istek (Cache HIT bekleniyor)..." -ForegroundColor White
    $start2 = Get-Date
    $response2 = Invoke-RestMethod -Uri "$baseUrl/api/member/getallpaginated?pageNumber=1&pageSize=5" -Method GET
    $end2 = Get-Date
    $duration2 = ($end2 - $start2).TotalMilliseconds
    Write-Host "     ✓ Tamamlandı. Süre: $([math]::Round($duration2, 2))ms" -ForegroundColor Green
    Write-Host "     ✓ Veri sayısı: $($response2.data.items.Count)" -ForegroundColor Green

    # Performans karşılaştırması
    $speedImprovement = [math]::Round((($duration1 - $duration2) / $duration1) * 100, 2)
    Write-Host "     📊 Performans iyileşmesi: %$speedImprovement" -ForegroundColor Magenta

    # Veri doğruluğu kontrolü
    if ($response1.data.items.Count -eq $response2.data.items.Count) {
        Write-Host "     ✅ BAŞARILI: Aynı veri döndü (Cache çalışıyor)" -ForegroundColor Green
        $test1Result = "SUCCESS"
    } else {
        Write-Host "     ❌ HATA: Farklı veri döndü" -ForegroundColor Red
        $test1Result = "FAILED"
    }

    $testResults += @{
        Test = "Member GetAllPaginated"
        Result = $test1Result
        FirstRequestMs = $duration1
        SecondRequestMs = $duration2
        SpeedImprovement = "$speedImprovement%"
        DataCount = $response1.data.items.Count
    }
} catch {
    Write-Host "     ❌ HATA: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += @{
        Test = "Member GetAllPaginated"
        Result = "ERROR"
        Error = $_.Exception.Message
    }
}

Write-Host ""
Write-Host "=== TEST SONUÇLARI ===" -ForegroundColor Yellow
$testResults | ForEach-Object {
    Write-Host "Test: $($_.Test)" -ForegroundColor Cyan
    Write-Host "Sonuç: $($_.Result)" -ForegroundColor $(if($_.Result -eq "SUCCESS") {"Green"} else {"Red"})
    if ($_.FirstRequestMs) {
        Write-Host "İlk İstek: $([math]::Round($_.FirstRequestMs, 2))ms" -ForegroundColor Gray
        Write-Host "İkinci İstek: $([math]::Round($_.SecondRequestMs, 2))ms" -ForegroundColor Gray
        Write-Host "Performans İyileşmesi: $($_.SpeedImprovement)" -ForegroundColor Magenta
    }
    Write-Host ""
}
