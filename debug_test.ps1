Write-Host "=== DEBUG TEST ===" -ForegroundColor Yellow

$baseUrl = "http://localhost:5165"

# Test 1: Basit member getall
Write-Host "TEST: Member GetAll (basit)" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/member/getall" -Method GET
    Write-Host "Success: $($response.success)" -ForegroundColor Green
    Write-Host "Message: $($response.message)" -ForegroundColor Green
    Write-Host "Data type: $($response.data.GetType().Name)" -ForegroundColor Green
    Write-Host "Data count: $($response.data.Count)" -ForegroundColor Green
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor Red
}

Write-Host ""

# Test 2: MembershipType getall
Write-Host "TEST: MembershipType GetAll" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/membershiptype/getall" -Method GET
    Write-Host "Success: $($response.success)" -ForegroundColor Green
    Write-Host "Message: $($response.message)" -ForegroundColor Green
    Write-Host "Data count: $($response.data.Count)" -ForegroundColor Green
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Pagination debug
Write-Host "TEST: Member GetAllPaginated Debug" -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/member/getallpaginated?pageNumber=1&pageSize=100" -Method GET
    Write-Host "Success: $($response.success)" -ForegroundColor Green
    Write-Host "Message: $($response.message)" -ForegroundColor Green
    Write-Host "Data type: $($response.data.GetType().Name)" -ForegroundColor Green
    if ($response.data.items) {
        Write-Host "Items count: $($response.data.items.Count)" -ForegroundColor Green
        Write-Host "Total count: $($response.data.totalCount)" -ForegroundColor Green
        Write-Host "Page number: $($response.data.pageNumber)" -ForegroundColor Green
        Write-Host "Page size: $($response.data.pageSize)" -ForegroundColor Green
    } else {
        Write-Host "No items property found" -ForegroundColor Red
        Write-Host "Data structure:" -ForegroundColor Yellow
        $response.data | ConvertTo-Json -Depth 2
    }
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "=== DEBUG COMPLETED ===" -ForegroundColor Yellow
