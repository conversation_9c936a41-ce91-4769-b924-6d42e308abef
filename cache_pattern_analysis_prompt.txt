GymProject'te kritik bir cache invalidation pattern hatası tespit ettim. Lütfen tüm Manager'ları bu spesifik hata açısından incele:

## 🚨 TESPİT EDİLEN HATA TİPİ:

### SORUN: Service Interface Adı vs Cache Key Mismatch

#### <PERSON><PERSON>k Hata (RemainingDebtManager'da bulundu):
```csharp
// ❌ YANLIŞ
[CacheRemoveAspect("gym:{companyId}:remainingdebt:*")]
public IResult AddDebtPayment(...)

// ✅ DOĞRU  
[CacheRemoveAspect("gym:{companyId}:iremainingdebtservice:*")]
public IResult AddDebtPayment(...)
```

### NEDEN HATALI:
1. **Cache Key Generation Logic:**
   - Interface: `IRemainingDebtService`
   - Cache key format: `gym:{companyId}:{interfaceName.ToLower()}:{methodName}:{params}`
   - <PERSON><PERSON><PERSON><PERSON> key: `gym:1:iremainingdebtservice:getremainingdebtdetails:noparams`

2. **Pattern Matching:**
   - <PERSON><PERSON><PERSON> pattern: `remainingdebt` 
   - Doğru pattern: `iremainingdebtservice`
   - Sonuç: Pattern eşleşmiyor → Cache silinmiyor!

## 🔍 İNCELEME TALİMATI:

### Her Manager için şu adımları takip et:

#### 1. **Interface Adını Tespit Et:**
```csharp
public class PaymentManager : IPaymentService
//                            ^^^^^^^^^^^^^ Bu interface adı
```

#### 2. **Cache Key Format'ını Hesapla:**
```
Interface: IPaymentService
Cache key prefix: ipaymentservice (I kaldırılıp lowercase)
```

#### 3. **CacheRemoveAspect Pattern'lerini Kontrol Et:**
```csharp
[CacheRemoveAspect("gym:{companyId}:payment:*")]        // ❌ YANLIŞ
[CacheRemoveAspect("gym:{companyId}:ipaymentservice:*")] // ✅ DOĞRU
```

#### 4. **Özellikle Kontrol Et:**
- `IPaymentService` → pattern: `ipaymentservice` olmalı
- `IMemberService` → pattern: `imemberservice` olmalı  
- `IProductService` → pattern: `iproductservice` olmalı
- `ITransactionService` → pattern: `itransactionservice` olmalı
- `IWorkoutProgramTemplateService` → pattern: `iworkoutprogramtemplateservice` olmalı

### 5. **RAPOR FORMAT:**
```
## ManagerAdı.cs
Interface: IServiceName
Beklenen cache prefix: iservicename

### CacheRemoveAspect Kontrolleri:
❌ HATALI: [CacheRemoveAspect("gym:{companyId}:wrongpattern:*")]
   - Gerçek pattern olmalı: iservicename
   - Hata: wrongpattern ≠ iservicename

✅ DOĞRU: [CacheRemoveAspect("gym:{companyId}:iservicename:*")]
```

## 🎯 ÖNEMLİ NOTLAR:

1. **Interface adından cache key'e dönüşüm:**
   - `I` prefix'i kaldırılır
   - Tüm harfler küçük yapılır
   - Örnek: `IPaymentService` → `ipaymentservice`

2. **Bu hata cache invalidation'ı tamamen bozar:**
   - CUD işlemleri yapılır
   - Cache silinmez (pattern eşleşmez)
   - Eski data cache'te kalır
   - Kullanıcı güncel olmayan data görür

3. **Test yöntemi:**
   - Cache'li endpoint'e istek at (cache yükle)
   - CUD işlemi yap
   - Aynı endpoint'e tekrar istek at
   - Eğer eski data geliyorsa pattern hatalı!

Lütfen TÜM Manager'ları bu spesifik hata açısından tek tek incele ve hatalı pattern'leri tespit et.
