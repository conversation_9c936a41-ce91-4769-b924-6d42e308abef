Write-Host "=== CACHE TEST STARTING ===" -ForegroundColor Yellow

$baseUrl = "http://localhost:5165"

# TEST 2: Member GetMemberDetailsPaginated
Write-Host "TEST 2: Member GetMemberDetailsPaginated (Cache: 1800s)" -ForegroundColor Cyan

try {
    Write-Host "First request (Cache MISS)..." -ForegroundColor White
    $start1 = Get-Date
    $response1 = Invoke-RestMethod -Uri "$baseUrl/api/member/getmemberdetailspaginated?pageNumber=1&pageSize=5" -Method GET
    $end1 = Get-Date
    $duration1 = ($end1 - $start1).TotalMilliseconds
    Write-Host "First request completed in $([math]::Round($duration1, 2))ms" -ForegroundColor Green
    Write-Host "Data count: $($response1.data.items.Count)" -ForegroundColor Green

    Start-Sleep -Seconds 2

    Write-Host "Second request (Cache HIT expected)..." -ForegroundColor White
    $start2 = Get-Date
    $response2 = Invoke-RestMethod -Uri "$baseUrl/api/member/getmemberdetailspaginated?pageNumber=1&pageSize=5" -Method GET
    $end2 = Get-Date
    $duration2 = ($end2 - $start2).TotalMilliseconds
    Write-Host "Second request completed in $([math]::Round($duration2, 2))ms" -ForegroundColor Green
    Write-Host "Data count: $($response2.data.items.Count)" -ForegroundColor Green

    $speedImprovement = [math]::Round((($duration1 - $duration2) / $duration1) * 100, 2)
    Write-Host "Speed improvement: $speedImprovement%" -ForegroundColor Magenta

    if ($response1.data.items.Count -eq $response2.data.items.Count) {
        Write-Host "SUCCESS: Cache is working!" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Different data returned" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "=== TEST COMPLETED ===" -ForegroundColor Yellow
