Write-Host "=== COMPREHENSIVE CACHE TEST ===" -ForegroundColor Yellow

$baseUrl = "http://localhost:5165"
$testResults = @()

# TEST 1: Member GetAllPaginated (Cache: 600s)
Write-Host "TEST 1: Member GetAllPaginated (Cache: 600s)" -ForegroundColor Cyan
try {
    $start1 = Get-Date
    $response1 = Invoke-RestMethod -Uri "$baseUrl/api/member/getallpaginated?pageNumber=1&pageSize=5" -Method GET
    $end1 = Get-Date
    $duration1 = ($end1 - $start1).TotalMilliseconds
    Write-Host "  MISS: $([math]::Round($duration1, 2))ms - Count: $($response1.data.items.Count)" -ForegroundColor White

    Start-Sleep -Seconds 2

    $start2 = Get-Date
    $response2 = Invoke-RestMethod -Uri "$baseUrl/api/member/getallpaginated?pageNumber=1&pageSize=5" -Method GET
    $end2 = Get-Date
    $duration2 = ($end2 - $start2).TotalMilliseconds
    Write-Host "  HIT:  $([math]::Round($duration2, 2))ms - Count: $($response2.data.items.Count)" -ForegroundColor Green

    $improvement1 = [math]::Round((($duration1 - $duration2) / $duration1) * 100, 2)
    Write-Host "  Performance: %$improvement1 improvement" -ForegroundColor Magenta
    $testResults += "TEST1: GetAllPaginated - $improvement1% improvement"
} catch {
    Write-Host "  ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# TEST 2: Member GetMemberDetailsPaginated (Cache: 1800s)
Write-Host "TEST 2: Member GetMemberDetailsPaginated (Cache: 1800s)" -ForegroundColor Cyan
try {
    $start1 = Get-Date
    $response1 = Invoke-RestMethod -Uri "$baseUrl/api/member/getmemberdetailspaginated?pageNumber=1&pageSize=5" -Method GET
    $end1 = Get-Date
    $duration1 = ($end1 - $start1).TotalMilliseconds
    Write-Host "  MISS: $([math]::Round($duration1, 2))ms - Count: $($response1.data.items.Count)" -ForegroundColor White

    Start-Sleep -Seconds 2

    $start2 = Get-Date
    $response2 = Invoke-RestMethod -Uri "$baseUrl/api/member/getmemberdetailspaginated?pageNumber=1&pageSize=5" -Method GET
    $end2 = Get-Date
    $duration2 = ($end2 - $start2).TotalMilliseconds
    Write-Host "  HIT:  $([math]::Round($duration2, 2))ms - Count: $($response2.data.items.Count)" -ForegroundColor Green

    $improvement2 = [math]::Round((($duration1 - $duration2) / $duration1) * 100, 2)
    Write-Host "  Performance: %$improvement2 improvement" -ForegroundColor Magenta
    $testResults += "TEST2: GetMemberDetailsPaginated - $improvement2% improvement"
} catch {
    Write-Host "  ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# TEST 3: RemainingDebts GetRemainingDebtDetails (Cache: 300s)
Write-Host "TEST 3: RemainingDebts GetRemainingDebtDetails (Cache: 300s)" -ForegroundColor Cyan
try {
    $start1 = Get-Date
    $response1 = Invoke-RestMethod -Uri "$baseUrl/api/remainingdebts/getremainingdebtdetails" -Method GET
    $end1 = Get-Date
    $duration1 = ($end1 - $start1).TotalMilliseconds
    Write-Host "  MISS: $([math]::Round($duration1, 2))ms - Count: $($response1.data.Count)" -ForegroundColor White

    Start-Sleep -Seconds 2

    $start2 = Get-Date
    $response2 = Invoke-RestMethod -Uri "$baseUrl/api/remainingdebts/getremainingdebtdetails" -Method GET
    $end2 = Get-Date
    $duration2 = ($end2 - $start2).TotalMilliseconds
    Write-Host "  HIT:  $([math]::Round($duration2, 2))ms - Count: $($response2.data.Count)" -ForegroundColor Green

    $improvement3 = [math]::Round((($duration1 - $duration2) / $duration1) * 100, 2)
    Write-Host "  Performance: %$improvement3 improvement" -ForegroundColor Magenta
    $testResults += "TEST3: GetRemainingDebtDetails - $improvement3% improvement"
} catch {
    Write-Host "  ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# TEST 4: Member GetTodayEntries (Cache: 300s)
Write-Host "TEST 4: Member GetTodayEntries (Cache: 300s)" -ForegroundColor Cyan
try {
    $start1 = Get-Date
    $response1 = Invoke-RestMethod -Uri "$baseUrl/api/member/gettodayentries?date=2025-06-11" -Method GET
    $end1 = Get-Date
    $duration1 = ($end1 - $start1).TotalMilliseconds
    Write-Host "  MISS: $([math]::Round($duration1, 2))ms - Count: $($response1.data.Count)" -ForegroundColor White

    Start-Sleep -Seconds 2

    $start2 = Get-Date
    $response2 = Invoke-RestMethod -Uri "$baseUrl/api/member/gettodayentries?date=2025-06-11" -Method GET
    $end2 = Get-Date
    $duration2 = ($end2 - $start2).TotalMilliseconds
    Write-Host "  HIT:  $([math]::Round($duration2, 2))ms - Count: $($response2.data.Count)" -ForegroundColor Green

    $improvement4 = [math]::Round((($duration1 - $duration2) / $duration1) * 100, 2)
    Write-Host "  Performance: %$improvement4 improvement" -ForegroundColor Magenta
    $testResults += "TEST4: GetTodayEntries - $improvement4% improvement"
} catch {
    Write-Host "  ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# TEST 5: MembershipType GetAll (Cache: 14400s)
Write-Host "TEST 5: MembershipType GetAll (Cache: 14400s)" -ForegroundColor Cyan
try {
    $start1 = Get-Date
    $response1 = Invoke-RestMethod -Uri "$baseUrl/api/membershiptype/getall" -Method GET
    $end1 = Get-Date
    $duration1 = ($end1 - $start1).TotalMilliseconds
    Write-Host "  MISS: $([math]::Round($duration1, 2))ms - Count: $($response1.data.Count)" -ForegroundColor White

    Start-Sleep -Seconds 2

    $start2 = Get-Date
    $response2 = Invoke-RestMethod -Uri "$baseUrl/api/membershiptype/getall" -Method GET
    $end2 = Get-Date
    $duration2 = ($end2 - $start2).TotalMilliseconds
    Write-Host "  HIT:  $([math]::Round($duration2, 2))ms - Count: $($response2.data.Count)" -ForegroundColor Green

    $improvement5 = [math]::Round((($duration1 - $duration2) / $duration1) * 100, 2)
    Write-Host "  Performance: %$improvement5 improvement" -ForegroundColor Magenta
    $testResults += "TEST5: MembershipType GetAll - $improvement5% improvement"
} catch {
    Write-Host "  ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== CACHE TEST SUMMARY ===" -ForegroundColor Yellow
$testResults | ForEach-Object { Write-Host "  $_" -ForegroundColor Green }
